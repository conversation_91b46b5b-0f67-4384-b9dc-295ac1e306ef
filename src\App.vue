<template>
  <div class="page-container">
    <el-container class="main-container">
      <el-main>
        <el-card class="app-container">
          <template #header>
            <h1 class="header-title">Bibliographic Items Tagging Tool</h1>
            <el-text>An AI-driven tool for tagging bibliographic items using tags from a tag pool. (e.g.IsisCB's tag
              pool,
              etc) (Ver 0.3)</el-text>
          </template>

          <!-- Model Selection Dropdown -->
          <!-- Floating button for advanced settings -->
          <div class="floating-button">
            <div><el-button v-if="results.length" type="primary" circle @click="toggleAllResults"
                :icon="activeNames.length === paginatedResults.length ? ZoomOut : ZoomIn" /></div>
            <div><el-button type="primary" circle @click="handleSettingsToggle" :icon="Setting" /></div>
            <div><el-button type="primary" circle @click="handleUserGuideToggle" :icon="QuestionFilled" /></div>
          </div>

          <SettingsDrawer ref="settingsDrawerRef" @settingsChanged="handleSettingsChanged"
            @drawerToggle="handleDrawerToggle" />

          <!-- User Guide Dialog -->
          <el-dialog v-model="showUserGuide" title="User Guide" :width="screenIsPortrait ? '95%' : '80%'"
            :fullscreen="screenIsPortrait" class="user-guide-dialog">
            <div class="user-guide-content" v-html="userGuideHtml"></div>
            <template #footer>
              <el-button type="primary" @click="showUserGuide = false">Close</el-button>
            </template>
          </el-dialog>

          <DataImporter :clearTrigger="clearTrigger" @itemsUpdated="handleItemsUpdated" />
          <ProcessingControls :biblioItems="biblioItems" :apiUrl="apiUrl" :selectedModel="selectedModel"
            @resultsUpdated="handleResultsUpdated" @allIndexedBiblioItemsUpdated="handleAllIndexedBiblioItemsUpdated"
            @clearAllData="handleClearAllData" @fetchAllTags="fetchAllTags" />

          <ResultsDisplay ref="resultsDisplayRef" :results="results" :allIndexedBiblioItems="allIndexedBiblioItems"
            :screenIsPortrait="screenIsPortrait" :tagNames="tagNames" :allTagCandidates="allTagCandidates"
            :apiAllTagsUrl="apiAllTagsUrl" :isFetchingTags="isFetchingTags" :hasLoadedTags="hasLoadedTags"
            @tagsUpdated="handleTagsUpdated" @stateUpdated="handleResultsDisplayStateUpdate" />

          <ExportControls :results="results" :allIndexedBiblioItems="allIndexedBiblioItems"
            :allTagCandidates="allTagCandidates" :deselectedTags="deselectedTags" :newTags="newTags"
            :currentSource="currentSource" :zoteroConfig="zoteroConfig" :zoteroBaseUrl="zoteroBaseUrl"
            :screenIsPortrait="screenIsPortrait" :apiAllTagsUrl="apiAllTagsUrl" :isFetchingTags="isFetchingTags"
            :hasLoadedTags="hasLoadedTags" @fetchAllTags="fetchAllTags" />
        </el-card>
      </el-main>
    </el-container>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import axios from 'axios'
import {
  ElContainer,
  ElMain,
  ElCard,
  ElButton,
  ElText,
  ElMessage,
  ElDialog
} from 'element-plus'
import { ZoomIn, ZoomOut, Setting, QuestionFilled } from '@element-plus/icons-vue'

// Import new components
import SettingsDrawer from './components/SettingsDrawer.vue'
import DataImporter from './components/DataImporter.vue'
import ProcessingControls from './components/ProcessingControls.vue'
import ResultsDisplay from './components/ResultsDisplay.vue'
import ExportControls from './components/ExportControls.vue'

// Settings state - now managed by SettingsDrawer component
const settingsDrawerRef = ref(null)
const screenIsPortrait = ref(false)
const selectedModel = ref('')
const apiUrl = ref('')
const apiAllTagsUrl = ref('')

const biblioItems = ref([]) // This is for storing the original items imported from data sources
const allIndexedBiblioItems = ref([]) // This is the indexed version of biblioItems. It will be populated when calling submitBiblioItems function.
const results = ref([]) // This is for storing the results from the API.

// Clear trigger for DataImporter
const clearTrigger = ref(0)

// Tag state for export functionality (received from ResultsDisplay)
const deselectedTags = ref(new Set())
const newTags = ref(new Map())

// Results display state for floating button functionality
const resultsDisplayRef = ref(null)
const currentPage = ref(1)
const pageSize = ref(10)

// Computed properties for pagination and toggle functionality
const paginatedResults = computed(() => {
  const startIndex = (currentPage.value - 1) * pageSize.value
  const endIndex = startIndex + pageSize.value
  return results.value.slice(startIndex, endIndex)
})

// Active names state - track which collapse items are expanded
const activeNames = ref([])

// TAG CONFIGURATION
const allTagCandidates = ref([]) // Store all tags from the curated tag pool
const tagNames = computed(() => allTagCandidates.value.map(tag => tag.name))
const isFetchingTags = ref(false)
const hasLoadedTags = ref(false) // Track if tags have been loaded

// USER GUIDE CONFIGURATION
const showUserGuide = ref(false)
const userGuideHtml = ref('')


// Settings handlers
const handleSettingsChanged = (settings) => {
  screenIsPortrait.value = settings.screenIsPortrait
  selectedModel.value = settings.selectedModel
  apiUrl.value = settings.apiUrl
  apiAllTagsUrl.value = settings.apiAllTagsUrl
}

const handleSettingsToggle = () => {
  if (settingsDrawerRef.value) {
    settingsDrawerRef.value.toggleDrawer()
  }
}

const handleDrawerToggle = (isVisible) => {
  // Handle drawer visibility changes if needed
  // This can be used for any parent-level logic when drawer opens/closes
}

// User guide handlers
const handleUserGuideToggle = async () => {
  if (!userGuideHtml.value) {
    await loadUserGuide()
  }
  showUserGuide.value = true
}

const loadUserGuide = async () => {
  try {
    // Import the markdown content directly
    const userGuideMarkdown = `### **User Guide: Bibliographic Items Tagging Tool**

Welcome to the AI-driven tool for tagging bibliographic items! This guide will walk you through the process of importing your data, generating tags, reviewing them, and exporting the final results.

#### **Quick Overview**

The workflow is broken down into three main steps, which are laid out in the application:

1.  **Import Items:** Add your bibliographic data from Zotero, a CSV file, or by manual entry.
2.  **Review and Edit Tags:** Use the AI to generate tags, then review and refine them.
3.  **Export Result:** Download your newly tagged data in various formats or save it back to Zotero.

You can access **Advanced Settings** at any time using the floating settings button (⚙️) on the right side of the screen.

---

### **Part 1: Import Bibliographic Items**

First, you need to import the items you want to tag. You have three options, presented in a collapsible menu.

#### **Method A: Import from Zotero (Recommended)**

This is the most powerful option, as it allows you to save tags back to your Zotero library later.

1.  **Library ID:** Enter your Zotero User ID (for a personal library) or Group ID (for a shared group library).
    * You can find your **User ID** in your Zotero security settings.
    * The **Group ID** is in the URL of your group's library page on the Zotero website.
2.  **API Key:** Create and enter a Zotero API key from your Zotero security settings. This is required for the tool to access your library.
3.  **Tag:** Enter a tag that you have applied in Zotero to the items you wish to import (e.g., \`to-be-tagged\`). The tool will fetch all items with this specific tag.
4.  Click **Fetch Zotero Items**. The tool will connect to your library and import the relevant items, showing a preview when complete.

#### **Method B: Upload CSV**

1.  Click the **Upload** button under the "Upload CSV" section.
2.  Select a CSV file from your computer.
3.  **CSV exported from Zotero:**
    * Keep this box **checked** (default) if your CSV file was exported from Zotero and has a standard header row. The tool will automatically map fields like \`Title\`, \`Abstract Note\`, \`Author\`, etc.
    * **Uncheck** this box if your CSV is a simple two-column file without a header. The tool will treat the first column as the \`title\` and the second as the \`abstract\`.

#### **Method C: Manually Add Items**

For quick additions or testing, you can add items one by one.

1.  Enter the **Title** and **Abstract** in the provided text boxes.
2.  Click **Add to List**.
3.  Use the **Remove Last Item** and **Undo Remove** buttons to manage your manually added list.

#### **Previewing Your Items**

Once you've imported items, a preview card will appear showing the total count and the titles of the first and last few items in your list. The source of the data (e.g., "Zotero," "local file") will also be displayed.

---

### **Part 2: Generating and Reviewing Tags**

After importing your items, you can generate tags for them.

1.  **Tag Items:** Click the green **Tag Items** button.
2.  **Batch Size:** The tool processes items in batches to communicate with the AI. A suggested batch size is provided, but you can set your own number. Smaller batches provide more frequent progress updates; larger batches can be slightly faster overall.
3.  **Processing:** A loading message will appear showing the elapsed time. Batches are processed one by one, and you will receive a success message as each batch is completed.

#### **Reviewing and Editing**

Once processing is finished, the results will appear in the "Step 2. Review and Edit Matched Tags" section.

* **Expand/Collapse:** You can expand each item to see its details or use the **Expand All** / **Collapse All** button for convenience.
* **Matched Tags:** This is the primary list of tags for the item.
    * **Deselect a Tag:** Simply click on a tag to deselect it. It will become grayed out and struck through, marking it for exclusion from the final export. Click it again to re-activate it.
    * **Add a New Tag:**
        1.  Start typing in the input box ("*Type to search or Enter to add...*").
        2.  A dropdown will appear with suggestions from the main tag pool. You can click a suggestion to add it.
        3.  To add a completely new custom tag, type your tag and press **Enter**.
    * **Remove an Added Tag:** Click the 'x' on any newly added tag to remove it.
* **Reference Tags (Concept, Person/Org, Time/Place):** These are additional keywords extracted by the AI for your reference. To add one to your main "Matched Tags" list, you can either **double-click** it or **drag-and-drop** it into the "Matched Tags" area.

---

### **Part 3: Exporting Your Results**

After reviewing and editing the tags, you can export your work.

#### **Saving to Zotero**

* If you imported your data from Zotero, the **Save Matched Tags to Zotero** button will be enabled.
* Clicking this will update the original items in your Zotero library, adding the new tags. It intelligently merges the new tags with any existing tags the item already had.

#### **Downloading Files**

1.  **Select Format:** Choose your desired file format: **RIS** (recommended for Zotero), **BibTeX**, or **CSV**.
2.  **Tag Formatting Options (Optional):** Before downloading, you can expand this section to customize how your tags are formatted.
    * **Add prefix/suffix to customized tags:** Adds a special mark (e.g., \`[NEW]\`) to tags that you created, which were not part of the original tag pool.
    * **Add metadata suffix to matched tags:** Appends metadata to tags, such as the tag's unique ID from the tag pool (e.g., \`my-tag [record_id]\`).
    * **Add custom suffix:** Appends a custom string (e.g., \`[IsisCBtag]\`) to all matched tags.
    * **Add an extra tag for all processed items:** Adds a single, consistent tag (e.g., \`processed-by-tagger\`) to every item that was processed.
3.  **Options:** For each format, you can click the **Options** button to customize the export. This allows you to select which data fields (e.g., Title, Author, Volume) to include in the exported file.
4.  **Download:** Click the **Download As** button to save the file to your computer.

---

### **Advanced Settings**

Click the floating gear icon (⚙️) to open the Advanced Settings drawer. Here you can:

* **Select Tag Generator:** Choose the AI model used for generating tags. \`gpt-4o-mini\` is the default, offering a balance of speed and quality. \`pytextrank\` is faster but less accurate.
* **Customize API's URL:** If you are hosting the backend service yourself or using a different endpoint, you can change the API URLs here.`

    // Convert markdown to HTML (basic conversion)
    userGuideHtml.value = convertMarkdownToHtml(userGuideMarkdown)
  } catch (error) {
    console.error('Error loading user guide:', error)
    userGuideHtml.value = '<p>Error loading user guide. Please try again later.</p>'
  }
}

const convertMarkdownToHtml = (markdown) => {
  let html = markdown
    // Headers (process from most specific to least specific)
    .replace(/^#### (.*$)/gim, '<h4>$1</h4>')
    .replace(/^### (.*$)/gim, '<h3>$1</h3>')
    .replace(/^## (.*$)/gim, '<h2>$1</h2>')
    .replace(/^# (.*$)/gim, '<h1>$1</h1>')
    // Bold
    .replace(/\*\*(.*?)\*\*/gim, '<strong>$1</strong>')
    // Italic (but not if it's part of bold)
    .replace(/(?<!\*)\*([^*]+?)\*(?!\*)/gim, '<em>$1</em>')
    // Code blocks
    .replace(/```([\s\S]*?)```/gim, '<pre><code>$1</code></pre>')
    // Inline code (escape backticks in the content)
    .replace(/\\`/gim, '&#96;')
    .replace(/`([^`]*?)`/gim, '<code>$1</code>')
    // Horizontal rules
    .replace(/^---$/gim, '<hr>')

  // Process lists more carefully
  const lines = html.split('\n')
  const processedLines = []
  let inList = false

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i]
    const isListItem = /^\d+\.\s+/.test(line) || /^\*\s+/.test(line)

    if (isListItem) {
      if (!inList) {
        if (/^\d+\.\s+/.test(line)) {
          processedLines.push('<ol>')
        } else {
          processedLines.push('<ul>')
        }
        inList = true
      }
      const content = line.replace(/^\d+\.\s+/, '').replace(/^\*\s+/, '')
      processedLines.push(`<li>${content}</li>`)
    } else {
      if (inList) {
        if (/^\d+\.\s+/.test(lines[i-1]) || /^\*\s+/.test(lines[i-1])) {
          processedLines.push('</ol>')
        } else {
          processedLines.push('</ul>')
        }
        inList = false
      }
      processedLines.push(line)
    }
  }

  if (inList) {
    processedLines.push('</ul>')
  }

  return processedLines.join('<br>')
    .replace(/<br><br>/g, '<br>')
    .replace(/<br><h/g, '<h')
    .replace(/<\/h([1-6])><br>/g, '</h$1>')
    .replace(/<br><hr>/g, '<hr>')
    .replace(/<hr><br>/g, '<hr>')
    .replace(/<br><ul>/g, '<ul>')
    .replace(/<\/ul><br>/g, '</ul>')
    .replace(/<br><ol>/g, '<ol>')
    .replace(/<\/ol><br>/g, '</ol>')
}

// Data source tracking (simplified)
const currentSource = ref('') // To track data source

// Zotero configuration (received from DataImporter when needed)
const zoteroConfig = ref(null)
const zoteroBaseUrl = ref('')

// Handle items updated from DataImporter
const handleItemsUpdated = (items, removedItems, source, zoteroData = null) => {
  biblioItems.value = items
  currentSource.value = source

  // Store Zotero configuration if provided
  if (source === 'Zotero' && zoteroData) {
    zoteroConfig.value = zoteroData.config
    zoteroBaseUrl.value = zoteroData.baseUrl
  } else if (source !== 'Zotero') {
    // Clear Zotero config when switching to non-Zotero sources
    zoteroConfig.value = null
    zoteroBaseUrl.value = ''
  }

  // Clear any existing results when new items are loaded
  results.value = []
  allIndexedBiblioItems.value = []
}

// Handle tags updated from ResultsDisplay
const handleTagsUpdated = (tagData) => {
  deselectedTags.value = tagData.deselectedTags
  newTags.value = tagData.newTags
}

// Handle results updated from ProcessingControls
const handleResultsUpdated = (newResults) => {
  results.value = newResults
}

// Handle indexed biblio items updated from ProcessingControls
const handleAllIndexedBiblioItemsUpdated = (indexedItems) => {
  allIndexedBiblioItems.value = indexedItems
}

// Handle clear all data from ProcessingControls
const handleClearAllData = () => {
  biblioItems.value = []
  allIndexedBiblioItems.value = []
  results.value = []
  currentSource.value = ''
  deselectedTags.value = new Set()
  newTags.value = new Map()
  // Clear Zotero config when clearing all data
  zoteroConfig.value = null
  zoteroBaseUrl.value = ''
  // Trigger DataImporter to clear its internal state
  clearTrigger.value++
}

// Function to fetch all tags
const fetchAllTags = async () => {
  if (hasLoadedTags.value) return // Don't fetch if already loaded

  try {
    isFetchingTags.value = true
    const response = await axios.get(`${apiAllTagsUrl.value}`)
    allTagCandidates.value = response.data
    hasLoadedTags.value = true
    ElMessage.success('Tag pool loaded successfully.')
  } catch (error) {
    console.error('Error fetching tags:', error)
    ElMessage.error('Failed to fetch tags')
    allTagCandidates.value = []
  } finally {
    isFetchingTags.value = false
  }
}













// Toggle all results function for floating button
const toggleAllResults = () => {
  if (resultsDisplayRef.value && resultsDisplayRef.value.toggleAllResults) {
    resultsDisplayRef.value.toggleAllResults()
  }
}

// Handle pagination and active names updates from ResultsDisplay
const handleResultsDisplayStateUpdate = (state) => {
  currentPage.value = state.currentPage
  pageSize.value = state.pageSize
  activeNames.value = state.activeNames
}

// Helper functions for components
const getAbstractByIndex = (index) => {
  const item = allIndexedBiblioItems.value.find(item => item.index === index)
  return item ? item.abstract : 'Abstract not found'
}
</script>

<style scoped>
.el-row {
  margin-bottom: 20px;
}

.el-divider--nowrap {
  text-align: center;
  white-space: nowrap;
  padding: 0;
  width: 100%;
  max-width: 100%;
}

.el-input-delimiter {
  width: 2rem;
}

.el-pagination {
  flex-wrap: wrap;
  gap: 5px;
}

.mark-position-select {
  width: 4rem;
  height: 1.8rem;
}

.center-content {
  display: flex;
  justify-content: center;
  /* Horizontally center child elements */
  align-items: center;
  /* Vertically center child elements */
  gap: 5px;
  /* Add spacing between child elements */
  flex-wrap: wrap;
  /* Ensure responsiveness if elements exceed available space */
  text-align: center;
}

.custom-input-number {
  width: 80px;
}

.button-container {
  display: flex;
  justify-content: center;
  /* Ensures the content inside <el-col> is centered */
  align-items: center;
  /* Aligns the button vertically within the container */
}

.page-container {
  min-height: 100vh;
  /* height: 100%; */
  width: 100%;
  display: flex;
  justify-content: center;
}

.main-container {
  width: 100%;
  max-width: 1200px;
  padding: 20px;
}

.app-container {
  width: 100%;
  height: 100%;
  margin: auto auto;
}


.header-title {
  margin: 0;
  font-size: 24px;
  color: var(--el-text-color-primary);
}

.article-form {
  width: 100%;
}

.article-input {
  margin-bottom: 30px;
  padding: 20px;
  border-radius: 8px;
  background-color: var(--el-bg-color);
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.form-item {
  margin-bottom: 20px;
}

.title-input,
.abstract-input {
  width: 100%;
}

.divider-title {
  font-size: 18px;
  font-weight: bold;
  color: var(--el-text-color-primary);
}

.action-buttons {
  display: flex;
  gap: 12px;
  margin: 20px 0;
  justify-content: center;
}

.csv-submit-button {
  margin-top: 20px;
}

.loading-message {
  text-align: center;
  margin: 20px 0;
  padding: 20px;
  background-color: var(--el-color-primary-light-9);
  border-radius: 4px;
  color: var(--el-text-color-primary);
}

.results {
  margin-top: 10px;
}

.wrap-text {
  white-space: pre-wrap;
  word-wrap: break-word;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.tag-item {
  margin: 0;
}

.el-tag {
  white-space: normal;
  /* Allow text to wrap */
  word-break: break-word;
  /* Break long words if necessary */
  min-height: 1.5rem;
  height: fit-content;
  max-width: 100%;
  /* Ensure the tag doesn't overflow its container */
}


/* For tag matching */
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

.loading-icon {
  width: 14px;
  height: 14px;
  animation: rotate 1s linear infinite;
}

.tag-input-container {
  position: relative;
  display: inline-block;
}

.tag-suggestions-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  width: flex;
  max-height: 300px;
  background: white;
  border: 1px solid var(--el-border-color);
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  z-index: 1000;
  overflow-y: auto;
}

.tag-suggestion-item {
  padding: 8px 12px;
  cursor: pointer;
  transition: background-color 0.3s;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.tag-suggestion-item:hover {
  background-color: var(--el-color-primary-light-9);
}

.loading-indicator {
  padding: 8px 12px;
  color: var(--el-text-color-secondary);
  text-align: center;
}

/* Add new styles for drag and drop */
.draggable-tag {
  cursor: move;
  transition: transform 0.2s;
}

.draggable-tag:hover {
  transform: scale(1.05);
}

.draggable-tag:active {
  cursor: grabbing;
}

.droppable-area {
  min-height: 40px;
  padding: 8px;
  border: 2px dashed transparent;
  border-radius: 4px;
  transition: all 0.3s;
}

.droppable-area:hover {
  border-color: var(--el-color-primary);
  background-color: var(--el-color-primary-light-9);
}

/* New styles for CSV preview */
.csv-section {
  margin: 20px 0;
  padding: 20px;
  background-color: var(--el-bg-color-overlay);
  border-radius: 8px;
}

.csv-preview {
  margin-top: 20px;
}

.preview-item {
  padding: 8px;
  margin: 4px 0;
  background-color: var(--el-bg-color);
  border-radius: 4px;
}

.preview-title {
  color: var(--el-text-color-primary);
  font-size: 14px;
}

.more-items {
  margin-top: 8px;
  color: var(--el-text-color-secondary);
  font-style: italic;
}

.download-section {
  margin-top: 20px;
  text-align: center;
}

.clickable-tag {
  cursor: pointer;
  transition: all 0.3s;
}

.deselected-tag {
  opacity: 0.5;
  text-decoration: line-through;
}

.new-tag {
  background-color: var(--el-color-success-light-9);
  border-color: var(--el-color-success);
  color: var(--el-color-success);
}

.tag-input {
  width: 280px;
  max-width: 100%;
  margin-left: 8px;
}

.floating-button {
  position: fixed;
  bottom: 30px;
  right: 30px;
  z-index: 1000;
  display: flex;
  align-items: center;
  flex-direction: column;
  gap: 10px;
}

.floating-button .el-button {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

/* Optional: Add hover effect for clickable tags */
.clickable-tag:hover {
  transform: scale(1.05);
}

.truncate-title {
  /* display: inline-block; */
  max-width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

:deep(.el-form-item__label) {
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 8px;
}

:deep(.el-card__header) {
  text-align: center;
  padding: 10px;
  background-color: var(--el-color-primary-light-9);
}

:deep(.el-collapse-item__header) {
  font-size: 16px;
  /* font-weight: bold; */
}

:deep(.el-descriptions__label) {
  font-weight: bold;
  width: 200px;
  padding-right: 16px;
}

:deep(.el-textarea__inner) {
  min-height: 120px !important;
}

:deep(.el-main) {
  padding: 0;
}

:deep(.el-descriptions__table) {
  width: 100%;
  /* Ensure the table takes full width of its container */
  max-width: 100%;
  /* Prevent overflow */
  table-layout: fixed;
  /* Ensure the table respects column widths */
}

:deep(.el-descriptions__cell) {
  word-break: break-word;
  /* Break long words to prevent overflow */
  white-space: normal;
  /* Allow text to wrap */
}


/* zotero-related */
.zotero-form {
  padding: 30px;
  background-color: var(--el-bg-color-overlay);
  border-radius: 0px;
  margin-bottom: 0px;
}

.source-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.fetch-button {
  width: 100%;
  margin-top: 16px;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

:deep(.el-collapse-item__header) {
  font-size: 16px;
}

:deep(.el-form-item__label) {
  font-weight: bold;
}

/* User Guide Dialog Styles */
.user-guide-dialog {
  max-height: 80vh;
}

.user-guide-content {
  max-height: 60vh;
  overflow-y: auto;
  padding: 20px;
  line-height: 1.6;
  font-size: 14px;
}

.user-guide-content h1 {
  color: var(--el-color-primary);
  font-size: 24px;
  margin-bottom: 16px;
  border-bottom: 2px solid var(--el-color-primary-light-8);
  padding-bottom: 8px;
}

.user-guide-content h2 {
  color: var(--el-color-primary);
  font-size: 20px;
  margin-top: 24px;
  margin-bottom: 12px;
}

.user-guide-content h3 {
  color: var(--el-text-color-primary);
  font-size: 16px;
  margin-top: 20px;
  margin-bottom: 10px;
  font-weight: bold;
}

.user-guide-content h4 {
  color: var(--el-text-color-primary);
  font-size: 14px;
  margin-top: 16px;
  margin-bottom: 8px;
  font-weight: bold;
}

.user-guide-content ul {
  margin: 12px 0;
  padding-left: 20px;
}

.user-guide-content li {
  margin: 6px 0;
  list-style-type: disc;
}

.user-guide-content code {
  background-color: var(--el-color-info-light-9);
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
  font-size: 13px;
}

.user-guide-content pre {
  background-color: var(--el-color-info-light-9);
  padding: 12px;
  border-radius: 6px;
  overflow-x: auto;
  margin: 12px 0;
}

.user-guide-content pre code {
  background: none;
  padding: 0;
}

.user-guide-content strong {
  font-weight: bold;
  color: var(--el-text-color-primary);
}

.user-guide-content em {
  font-style: italic;
}

.user-guide-content hr {
  border: none;
  border-top: 1px solid var(--el-border-color);
  margin: 20px 0;
}
</style>